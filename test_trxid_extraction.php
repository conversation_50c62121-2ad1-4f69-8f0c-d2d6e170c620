<?php
// Test script to verify transaction ID extraction for AMS messages

function extractTransactionId($text, $sender) {
    // Try multiple patterns based on sender
    $patterns = [
        '/Transaction\s+ID\s+([A-Z0-9\.]+)/i',  // "Transaction ID BD070725235717108632" (AMS format)
        '/Transaction\s+number\s+is\s+([A-Z0-9\.]+)/i',  // "Transaction number is R250707.2330.400094"
        '/TrxId[:\s]+([A-Z0-9\.]+)/i',  // "TrxId: R250707.2330.400094"
        '/ID[:\s]+([A-Z0-9\.]+)/i',  // "ID: R250707.2330.400094"
        '/Ref[:\s]+([A-Z0-9\.]+)/i',  // "Ref: R250707.2330.400094"
        '/([A-Z]\d{6}\.\d{4}\.\d{6})/i'  // Pattern like R250707.2330.400094
    ];

    foreach($patterns as $pattern) {
        if(preg_match($pattern, $text, $matches)) {
            return trim($matches[1], '.');
        }
    }

    // Fallback to existing methods based on sender
    if(preg_match('/8383/i', $sender)) {
        return trxidrobi($text);
    } else if(preg_match('/FlexiLoad|Skitto/i', $sender)) {
        return trxidbl($text);
    } else if(preg_match('/bKash|NAGAD|upay/i', $sender)) {
        return trxidbk($text);
    }

    return 'AUTO_' . time(); // Generate fallback transaction ID
}

function trxidbl($msg){
    // First try to match "Transaction ID" pattern for AMS messages
    if(preg_match('/Transaction\s+ID\s+([A-Z0-9]+)/i', $msg, $matches)) {
        return trim($matches[1], '.');
    }
    
    // Original logic for other formats
    preg_match('[ID]', $msg,$m);

    $searchd = preg_match_all("/$m[0].([a-zA-Z0-9]+)(.+)\ /isU",$msg,$matche);
    $pieces = explode(" ",$matche[0][0]);
    $int= $pieces[1];
    if(empty($int)){
       $pieces = explode(' ', $msg);
       $int = array_pop($pieces);
       $int = str_replace('.', '', $int);
    }
    return $int;
}

function trxidrobi($msg){
    // Placeholder for Robi transaction ID extraction
    return 'ROBI_' . time();
}

function trxidbk($msg){
    // Placeholder for bKash/NAGAD transaction ID extraction
    return 'BK_' . time();
}

// Test with your AMS message
$ams_message = "AMS 210: Recharge of 20.00 BDT to 8801778318921 is successful. Your new balance is 724.98 BDT. Transaction ID BD070725235717108632.";
$sender = "AMS";

echo "Testing AMS message:\n";
echo "Message: " . $ams_message . "\n";
echo "Sender: " . $sender . "\n";

$extracted_id = extractTransactionId($ams_message, $sender);
echo "Extracted Transaction ID: " . $extracted_id . "\n";

// Test with trxidbl function directly
echo "\nTesting trxidbl function directly:\n";
$extracted_id_bl = trxidbl($ams_message);
echo "Extracted Transaction ID (trxidbl): " . $extracted_id_bl . "\n";

// Expected result
echo "\nExpected: BD070725235717108632\n";
echo "Match: " . ($extracted_id == 'BD070725235717108632' ? 'YES' : 'NO') . "\n";
?>
